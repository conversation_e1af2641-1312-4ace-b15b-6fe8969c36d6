<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:orientation="vertical">
        <ImageView
            android:id="@+id/player_loading_center"
            android:layout_width="@dimen/x65"
            android:layout_height="@dimen/y65"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/y10"
            android:scaleType="fitXY" />

        <ImageView
            android:layout_width="@dimen/x73"
            android:layout_height="@dimen/y8"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:scaleType="fitXY" />

    </LinearLayout>
</FrameLayout>