package com.sh.ott.video.view

import android.content.Context
import android.util.AttributeSet
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.sh.ott.video.base.component.Resolution
import com.sh.ott.video.base.component.ShDataSource
import com.sh.ott.video.base.component.ShPlayerConstants
import com.sh.ott.video.player.PlayerConstants
import com.sh.ott.video.ShVideoLogger
import com.sh.ott.video.player.annotation.ScreenMode
import com.sh.ott.video.player.base.BaseOptionModel
import com.sh.ott.video.player.base.OnStateChangeListener
import com.sh.ott.video.player.base.PlayerFactory
import com.sh.ott.video.player.controller.VideoController
import com.sh.ott.video.player.controller.component.ControlComponent
import com.sh.ott.video.player.render.RenderFactory
import com.sh.ott.video.base.IShVideoController
import com.sh.ott.video.ShPlayerConfig
import com.sh.ott.video.ad.AdFLogoCallBack
import com.sh.ott.video.ad.AdHeaderCallBack
import com.sh.ott.video.ad.AdPauseCallBack
import com.sh.ott.video.ad.AdRequestFLogo
import com.sh.ott.video.ad.AdRequestFactory
import com.sh.ott.video.ad.AdStartPageCallBack
import com.sh.ott.video.ad.AdVideoView
import com.sh.ott.video.ad.AdVideoView.Companion.mOtherPlayerFactory
import com.sh.ott.video.ad.IAdCallEvent
import com.sh.ott.video.base.OnProgressChangedListener
import com.sh.ott.video.base.OnSkipStartAndEndListener
import com.sh.ott.video.contor.IShControlComponent
import com.sh.ott.video.contor.ShFilmVideoController
import com.sh.ott.video.contor.ShVideoViewController
import com.sh.ott.video.film.FilmVideoView
import com.sh.ott.video.film.IFilmCallEvent
import com.sh.ott.video.player.sofa.SofaOptionModel
import com.sh.ott.video.player.sofa.SofaPlayerFactory
import com.sh.ott.video.player.time.Interval
import com.sh.ott.video.player.time.IntervalStatus
import com.sh.ott.video.util.VideoStartTimeUtil
import com.sh.ott.video.util.createBaseRequestComponent
import com.sh.ott.video.util.getGidStringParams
import com.sh.ott.video.util.getVideoAidParams
import com.sh.ott.video.util.getVideoAreaIdParams
import com.sh.ott.video.util.getVideoCateCodeParams
import com.sh.ott.video.util.getVideoLengthParams
import com.sohu.ott.ads.sdk.model.RequestComponent
import java.sql.Time
import java.util.concurrent.TimeUnit

class ShPlayerContainer : FrameLayout, IShVideoController, IAdCallEvent, IFilmCallEvent {
    private var mAdVideo: AdVideoView? = null
    private var mAdTsVideo: AdVideoView? = null


    private var mFilmVideo: FilmVideoView? = null

    private var mAdPlayerFactory = ShPlayerConfig.adPlayerFactory

    private var mFilmPlayerFactory = ShPlayerConfig.filmPlayerFactory

    @Volatile
    private var isAddAdView = false
    private var isAddAdTsView = false

    @Volatile
    private var isAddFilmView = false

    private var mDataSource: ShDataSource? = null

    private var mAdStateChangeListener: AdStateChangeListener = AdStateChangeListener()
    private var mAdTsStateChangeListener: AdTsStateChangeListener = AdTsStateChangeListener()
    private var mFilmStateChangeListener: FilmStateChangeListener = FilmStateChangeListener()


    /**
     * 设置控制器，传null表示移除控制器
     */
    private var videoViewController: ShVideoViewController? = null


    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(
        context,
        attrs,
        0
    )

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int
    ) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        initAd()
        initFilm()
        initAdTs()
        ShVideoLogger.d("ShPlayerContainer isHardwareAccelerated $isHardwareAccelerated")
    }

    /**
     * 初始化广告 View
     */
    private fun initAd() {
        mAdVideo = AdVideoView(context)
        mAdVideo?.addOnStateChangeListener(mAdStateChangeListener)
        mAdVideo?.enableAutoPlay = true
        //设置广告播放器的播放内核
        mAdVideo?.setPlayerFactory(mAdPlayerFactory)
        mAdVideo?.setIAdCallEvent(this)
    }

    /**
     * 初始化 正片 View
     */
    private fun initFilm() {
        mFilmVideo = FilmVideoView(context)
        mFilmVideo?.addOnStateChangeListener(mFilmStateChangeListener)
        mFilmVideo?.getFilmViewControl()
            ?.addProgressChangedListener(object : OnProgressChangedListener {
                override fun onVideoProgressChanged(duration: Long, position: Long) {
                    super.onVideoProgressChanged(duration, position)
                    Log.d("onVideoProgressChanged", "pos :$position")
                    if (position / 1000 == 6L&&!adTsHasCompleted) {
                        mAdTsVideo?.loadAd("https://cdn2aty.vod.ystyt.aisee.tv/huiju/202506240958460142-mptc_tvad_685623_mptc_tvad_685623_tv_S_2p_095827_6.mp4?sign=1753792261-XrTEcWR9e-0-62866e404c49041e2e5e6decd400da0b")
                    }
                }
            })

        mFilmVideo?.setPlayerFactory(mFilmPlayerFactory)
        mFilmVideo?.setIFilmCallEvent(this)
        Log.d("onVideoProgressChanged", "add")
    }

    private fun initAdTs() {
        mAdTsVideo = AdVideoView(context)
        mAdTsVideo?.setOption(SofaOptionModel().also {
            it.decodeType=false
        })
        mAdTsVideo?.addOnStateChangeListener(mAdTsStateChangeListener)
        mAdTsVideo?.enableAutoPlay = true
        //设置广告播放器的播放内核
        mAdTsVideo?.setPlayerFactory(SofaPlayerFactory.create())

//        mAdTsVideo?.getAdViewControl()?.addProgressChangedListener(object : OnProgressChangedListener{
//            override fun onVideoProgressChanged(duration: Long, position: Long) {
//                super.onVideoProgressChanged(duration, position)
//                Log.d("onVideoProgressChanged", "pos :$position")
//
//            }
//        })
    }

    /**
     * 设置当前播放器 正片播放内核
     */
    override fun setFilmPlayerFactory(playerFactory: PlayerFactory) {
        mFilmPlayerFactory = playerFactory
        mFilmVideo?.setPlayerFactory(mFilmPlayerFactory)
    }

    /**
     * 设置当前播放器 广告播放内核
     */
    override fun setAdPlayerFactory(playerFactory: PlayerFactory) {
        mAdPlayerFactory = playerFactory
        mAdVideo?.setPlayerFactory(mAdPlayerFactory)
    }

    /**
     * 设置画面比例
     */
    override fun setFilmScreenAspectRatioType(aspectRatioType: Int) {
        mFilmVideo?.setScreenAspectRatioType(aspectRatioType)
    }

    override fun addFilmOnStateChangeListener(stateChangeListener: OnStateChangeListener) {
        mFilmVideo?.addOnStateChangeListener(stateChangeListener)
    }

    override fun addFilmOnSkipStartAndEndListener(skipStartAndEndListener: OnSkipStartAndEndListener) {
        mFilmVideo?.addFilmOnSkipStartAndEndListener(skipStartAndEndListener)
    }

    override fun addAdOnStateChangeListener(stateChangeListener: OnStateChangeListener) {
        mAdVideo?.addOnStateChangeListener(stateChangeListener)
    }

    override fun addOnStateChangeListener(stateChangeListener: OnStateChangeListener) {
        addFilmOnStateChangeListener(stateChangeListener)
        addAdOnStateChangeListener(stateChangeListener)
    }

    override fun removeFilmOnStateChangeListener(stateChangeListener: OnStateChangeListener) {
        mFilmVideo?.removeOnStateChangeListener(stateChangeListener)
    }

    override fun removeFilmOnSkipStartAndEndListener(skipStartAndEndListener: OnSkipStartAndEndListener) {
        mFilmVideo?.removeFilmOnSkipStartAndEndListener(skipStartAndEndListener)
    }

    override fun removeAdOnStateChangeListener(stateChangeListener: OnStateChangeListener) {
        mAdVideo?.removeOnStateChangeListener(stateChangeListener)
    }

    override fun removeOnStateChangeListener(stateChangeListener: OnStateChangeListener) {
        removeFilmOnStateChangeListener(stateChangeListener)
        removeAdOnStateChangeListener(stateChangeListener)
    }

    override fun addFilmProgressChangedListener(progressChangedListener: OnProgressChangedListener) {
        if (mFilmVideo?.videoController is ShFilmVideoController) {
            (mFilmVideo?.videoController as ShFilmVideoController).addProgressChangedListener(
                progressChangedListener
            )
        }
    }

    override fun removeFilmProgressChangedListener(progressChangedListener: OnProgressChangedListener) {
        if (mFilmVideo?.videoController is ShFilmVideoController) {
            (mFilmVideo?.videoController as ShFilmVideoController).removeProgressChangedListener(
                progressChangedListener
            )
        }
    }

    override fun addVideoControlComponent(controlComponent: IShControlComponent) {
        videoViewController?.addControlComponent(controlComponent)
    }

    override fun removeVideoControlComponent(controlComponent: IShControlComponent) {
        videoViewController?.removeControlComponent(controlComponent)
    }

    override fun setShVideoViewControl(videoViewController: ShVideoViewController?) {
        this.videoViewController = videoViewController
    }


    /**
     * 添加广告控制层
     */
    override fun addAdVideoControlComponent(controlComponent: ControlComponent) {
        mAdVideo?.videoController?.addControlComponent(controlComponent)
    }

    override fun addAdVideoControlComponent(
        controlComponent: ControlComponent,
        isDissociate: Boolean
    ) {
        mAdVideo?.videoController?.addControlComponent(controlComponent, isDissociate)
    }

    /**
     * 添加正片控制层
     */
    override fun addFilmVideoControlComponent(controlComponent: ControlComponent) {
        mFilmVideo?.videoController?.addControlComponent(controlComponent)
    }

    override fun addFilmVideoControlComponent(
        controlComponent: ControlComponent,
        isDissociate: Boolean
    ) {
        mFilmVideo?.videoController?.addControlComponent(controlComponent, isDissociate)
    }


    /**
     * 添加广告控制层
     */
    override fun removeAdVideoControlComponent(controlComponent: ControlComponent) {
        mAdVideo?.videoController?.removeControlComponent(controlComponent)
    }

    /**
     * 添加正片控制层
     */
    override fun removeFilmVideoControlComponent(controlComponent: ControlComponent) {
        mFilmVideo?.videoController?.removeControlComponent(controlComponent)
    }

    override fun removeAllVideoControlComponent() {
        mAdVideo?.videoController?.removeAllControlComponent()
        mFilmVideo?.videoController?.removeAllControlComponent()
        videoViewController?.removeAllControlComponent()
    }

    override fun adContainsControlComponents(key: ControlComponent): Boolean {
        return mAdVideo?.videoController?.containsControlComponents(key) ?: false
    }

    override fun filmContainsControlComponents(key: ControlComponent): Boolean {
        return mFilmVideo?.videoController?.containsControlComponents(key) ?: false
    }

    fun stopPauseAdVideo() {
        removeAd()
        ShVideoLogger.d("stopPauseAdVideo fun  ")
    }

    /**
     * 请求角标广告
     */
    private fun requestLogoAd() {
        onAdRequestType(ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_FLOGO)
        val requestComponent = mDataSource.createBaseRequestComponent()
        with(requestComponent) {
            val map = mutableMapOf<String, String>()
            map["al"] = mDataSource.getVideoAidParams()
            map["ar"] = mDataSource.getVideoAreaIdParams()
            map["vc"] = mDataSource.getVideoCateCodeParams()
            extendParam = map
        }
        AdRequestFLogo.getInstants()
            .requestFLogoAd(context, requestComponent, object : AdFLogoCallBack {
                /**
                 * 角标请求成功
                 * @param showTime 显示时间
                 * @param url 显示资源地址
                 */
                override fun onAdFLogoSuccess(showTime: Int, url: String?) {
                    mFilmVideo?.getFilmViewControl()?.onAdLogoSuccess(showTime, url)
                }

                /**
                 * 角标广告出错
                 */
                override fun onAdFLogoError(iAdsLoadedError: String?) {
                    mFilmVideo?.getFilmViewControl()
                        ?.onAdLogoError(iAdsLoadedError ?: "角标广告出错")
                }

            })

    }

    /**
     * 请求暂停广告
     */
    fun requestPauseAd() {
        ShVideoLogger.d("requestPauseAd fun  ")
        mAdVideo?.setPlayerFactory(AdVideoView.mMaxPlayerFactory)
        onAdRequestType(ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_PAUSE)
        val requestComponent = mDataSource.createBaseRequestComponent()
        with(requestComponent) {
            val map = mutableMapOf<String, String>()
            map["al"] = mDataSource.getVideoAidParams()
            map["ar"] = mDataSource.getVideoAreaIdParams()
            map["vc"] = mDataSource.getVideoCateCodeParams()
            extendParam = map
        }
        AdRequestFactory.getInstants()
            .requestPauseAd(context, requestComponent, object : AdPauseCallBack {
                /**
                 * 暂停广告出错
                 */
                override fun onAdPauseError(iAdsLoadedError: String?) {
                    mFilmVideo?.getFilmViewControl()?.onAdPauseError(iAdsLoadedError)
                    ShVideoLogger.d("onAdPauseError fun :${iAdsLoadedError.toString()} ")

                }

                /**
                 * 暂停图片
                 */


                override fun onAdPauseImage(url: MutableList<String?>) {
                    mFilmVideo?.getFilmViewControl()?.onAdPauseImage(url)
                    ShVideoLogger.d("onAdPauseImage fun  url:$url")

                }

                /**
                 * 暂停图片
                 */

                override fun onAdPauseMaxImage(url: String?) {
                    mFilmVideo?.getFilmViewControl()?.onAdPauseMaxImage(url)
                    ShVideoLogger.d("onAdPauseMaxImage fun  url:$url")

                }

                /**
                 * 暂停视频
                 */
                override fun onAdPauseVideo(url: String?) {
                    if (mFilmVideo?.playerPlaying == true) return
                    mFilmVideo?.getFilmViewControl()?.onAdPauseVideo(url)
                    if (!isAddAdView) {
                        addAd()
                    }
                    mAdVideo?.renderFactory = RenderFactory.textureViewRenderFactory()
                    mAdVideo?.setAdPath(url, true)
                    ShVideoLogger.d("onAdPauseVideo fun  isAddAdView:$isAddAdView  url:$url")
                }

            })
    }


    /**
     * 设置参数
     */
    override fun setDataSource(dataSource: ShDataSource?) {
        mDataSource = dataSource
        //先判断是否跳过广告 区分先添加哪个播放器 view
        if (dataSource?.adSkip == true) {
            if (isAddAdView) {
                removeAd()
            }
            addFilm()
        } else {
            if (isAddFilmView) {
                removeFilm()
            }
            addAd()
        }
        mAdVideo?.setDataSource(dataSource)
        mFilmVideo?.setDataSource(dataSource)
        ShVideoLogger.i("setDataSource fun is ${dataSource.toString()}")
    }

    /**
     * 添加 广告 view 到此视图
     */
    @Synchronized
    private fun addAd() {
        if (isAddAdView) {
            return
        }
        isAddAdView = true
        mAdVideo?.let {
            addView(it)
        }
        ShVideoLogger.d("addAd fun  ")
    }

    @Synchronized
    private fun addAdTs() {
        if (isAddAdTsView) {
            return
        }
        isAddAdTsView = true
        mAdTsVideo?.let {
            addView(it)
        }
        ShVideoLogger.d("addAdTs fun  ")
    }


    /**
     *  从此视图移除 广告 view
     */
    @Synchronized
    private fun removeAd() {
        mAdVideo?.release()
        if (isAddAdView) {
            isAddAdView = false
            mAdVideo?.let {
                removeView(it)
            }
        }
        ShVideoLogger.d("removeAd fun  ")
    }

    @Synchronized
    private fun removeAdTs() {
        mAdTsVideo?.release()
        if (isAddAdTsView) {
            isAddAdTsView = false
            mAdTsVideo?.let {
                removeView(it)
            }
        }
        ShVideoLogger.d("removeAdTs fun  ")
    }

    /**
     * 添加 正片 view 到此视图
     */
    @Synchronized
    private fun addFilm() {
        if (isAddFilmView) {
            return
        }
        isAddFilmView = true
        mFilmVideo?.let {
            addView(it)
        }
        ShVideoLogger.d("addFilm fun ")
    }

    /**
     *  从此视图移除 正片 view
     */
    @Synchronized
    private fun removeFilm() {
        resetShow()
        mFilmVideo?.release()
        if (isAddFilmView) {
            isAddFilmView = false
            mFilmVideo?.let {
                removeView(it)
            }
        }
        ShVideoLogger.d("removeFilm fun ")
    }

    /**
     * 异步准备资源
     */
    override fun prepareAsync() {
        if (mDataSource?.adSkip == false) {
            prepareAd()
        } else {
            prepareFilm()
        }
        ShVideoLogger.i("prepareAsync fun  isAddAdView:$isAddAdView isAddFilmView:$isAddFilmView ")
    }

    private fun prepareAd() {
        if (!isAddAdView) {
            removeFilm()
            addAd()
        } else {
            mAdVideo?.release()
        }
        mAdVideo?.playerState == PlayerConstants.VideoState.PREPARING
        when (mDataSource?.adType) {
            ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_OPEN -> {
                requestOpenVideoAd()
            }

            ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_HEADER -> {
                requestHeaderVideoAd()
            }
        }
        ShVideoLogger.d("prepareAd fun ")
    }


    /**
     * 请求前贴广告
     * 需要播放正片
     */
    private fun requestHeaderVideoAd() {
        mAdVideo?.setPlayerFactory(mOtherPlayerFactory)
        ShVideoLogger.i("requestHeaderVideoAd fun  ")
        onAdRequestType(ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_VIDEO_HEADER)
        val requestComponent = mDataSource.createBaseRequestComponent()
        with(requestComponent) {
            val map = mutableMapOf<String, String>()
            map["du"] = mDataSource.getVideoLengthParams()
            map["al"] = mDataSource.getVideoAidParams()
            map["ar"] = mDataSource.getVideoAreaIdParams()
            map["vc"] = mDataSource.getVideoCateCodeParams()
            extendParam = map
            player = mAdVideo
        }
        mAdVideo?.renderFactory = RenderFactory.SDK_INT28
        AdRequestFactory.getInstants()
            .requestHeaderVideoAd(context, requestComponent, object : AdHeaderCallBack {
                override fun onAdHeaderStarted() {
                    videoViewController?.onAdHeaderStarted()
                }

                override fun onAdHeaderCompleted() {
                    onHeaderCompleted()
                    ShVideoLogger.i("requestHeaderVideoAd fun onHeaderCompleted    ")
                }

                override fun onAdHeaderVideoTime(time: Int) {
                    onAdVideoTime(time)
                    ShVideoLogger.i("requestHeaderVideoAd fun onAdHeaderVideoTime  time:$time   ")

                }

                override fun onAdHeaderLoadedError(iAdsLoadedError: String?) {
                    onHeaderCompleted()
                    ShVideoLogger.i("requestHeaderVideoAd fun onAdHeaderLoadedError  iAdsLoadedError:$iAdsLoadedError   ")
                }

            })

    }


    /**
     * 请求开屏广告、
     *
     */
    private fun requestOpenVideoAd() {
        mAdVideo?.setPlayerFactory(mOtherPlayerFactory)
        ShVideoLogger.w("requestOpenVideoAd fun  ")
        onAdRequestType(ShPlayerConstants.AdRequestType.AD_REQUEST_TYPE_OPEN)
        val requestComponent = RequestComponent().apply {
            tuv = mDataSource.getGidStringParams()
            site = "1"
        }
        mAdVideo?.renderFactory = RenderFactory.surfaceViewRenderFactory()
        AdRequestFactory.getInstants()
            .requestStartPageAd(context, requestComponent, object : AdStartPageCallBack {
                override fun onAdStartPageVideo(url: String?, map: MutableMap<String, Any?>) {
                    ShVideoLogger.i("requestOpenVideoAd fun onAdStartPageVideo  url:$url  map:${map.toString()}")
                    mAdVideo?.mAdViewControl?.onAdStartVideo(url!!, map)
                    mAdVideo?.loadAd(url)
                }

                override fun onAdStartPageImage(url: String?, map: MutableMap<String, Any?>) {
                    ShVideoLogger.i("requestOpenVideoAd fun onAdStartPageImage  url:$url  map:${map.toString()}  ")
                    mAdVideo?.mAdViewControl?.onAdStartImage(url!!, map)
                }

                /**
                 * 开屏请求错误
                 */
                override fun onAdStartPageError(iAdsLoadedError: String?) {
                    ShVideoLogger.i("requestOpenVideoAd fun onAdStartPageError  iAdsLoadedError:$iAdsLoadedError   ")
                    onStartCompleted()
                    mAdVideo?.mAdViewControl?.onAdOpenError()
                }

            })

    }


    private fun onAdRequestType(type: Int) {
        mAdVideo?.onAdRequestType(type)
        mFilmVideo?.getFilmViewControl()
            ?.onAdRequestType(type)
        videoViewController?.onAdRequestType(type)
    }

    private fun onAdVideoTime(time: Int) {
        mAdVideo?.mAdViewControl?.onAdVideoTime(time)
        videoViewController?.onAdVideoTime(time)
    }


    private fun prepareFilm() {
//        if (mDataSource?.destroyAdLoader == true) {
//            AdRequestFactory.getInstants().destroyLoader()
//        }
        removeAd()
        VideoStartTimeUtil.getInstants().prepareFilmTime()
        if (isAddFilmView) {
            startFilm()
        } else {
            addFilm()
            startFilm()
        }
        ShVideoLogger.d("prepareFilm fun ")
    }

    private fun startFilm() {
        mFilmVideo?.release()
        mFilmVideo?.start()
        ShVideoLogger.d("startFilm fun ")
    }

    /**
     * 起播
     */
    override fun start() {
        if (isAddFilmView && mFilmVideo?.isInPlaybackState == true) {
            mFilmVideo?.startInPlaybackState()
        }
        ShVideoLogger.d("start fun ")
    }

    /**
     * 暂停
     */
    override fun pause() {
        if (isAddAdView) {
            mAdVideo?.pause()
        }
        if (isAddFilmView) {
            mFilmVideo?.pause()
        }
        ShVideoLogger.d("pause fun ")
    }

    /**
     * 自动控制播放暂停
     */
    override fun automaticPlayOrPause() {
        if (isAddAdView) {
            mAdVideo?.videoController?.togglePlay()
        }
        if (isAddFilmView) {
            mFilmVideo?.videoController?.togglePlay()
        }
    }

    /**
     * 回收资源
     */

    override fun release(clearState: Boolean, clearRender: Boolean) {
        destroyAdLoader()
        adTsHasCompleted=false
        cancelAdTsCountdown()
        mAdTsVideo?.release()
        mAdVideo?.release(clearState, clearRender)
        mFilmVideo?.release(clearState, clearRender)
        resetShow()
    }

    fun destroyAdLoader() {
        AdRequestFactory.getInstants().destroyLoader()
        AdRequestFLogo.getInstants().destroy()
    }


    /**
     * 销毁
     */
    override fun destroy() {
        VideoStartTimeUtil.getInstants().onDestroy()
        videoViewController?.removeAllControlComponent()
        videoViewController = null
        mAdVideo?.destroy()
        mFilmVideo?.destroy()
        removeAd()
        removeFilm()
        mAdVideo = null
        mFilmVideo = null
        mDataSource = null
        ShVideoLogger.d("destroy fun  ")
    }

    /**
     *  恢复
     */
    override fun resume() {
        if (isAddAdView) {
            mAdVideo?.resume()
        }
        if (isAddFilmView) {
            mFilmVideo?.resume()
        }
        ShVideoLogger.d("resume fun isAddAdView:$isAddAdView isAddFilmView:$isAddFilmView ")
    }

    /**
     * 重新播放
     */
    override fun replay(resetPosition: Boolean) {
        if (isAddFilmView) {
            mFilmVideo?.replay(resetPosition)
        }
        ShVideoLogger.d("replay fun ")
    }


    override fun onHeaderCompleted() {
        videoViewController?.onHeaderCompleted()
        prepareFilm()
        ShVideoLogger.d("onHeaderCompleted fun ")
    }

    override fun onStartCompleted() {
        prepareFilm()
        ShVideoLogger.d("onStartCompleted fun ")
    }


    /**
     * 通知广告/正片和当前View 窗体模式方式改变
     */
    fun notifyScreenMode(@ScreenMode mode: Int) {
        mFilmVideo?.screenMode = mode
        mAdVideo?.screenMode = mode
    }

    override fun seekTo(long: Long) {
        mFilmVideo?.seekTo(long)

    }

    override fun setSpeed(speed: Float) {
        mFilmVideo?.speed = speed
    }

    override fun setFilmMute(isMute: Boolean) {
        mFilmVideo?.isMute = isMute
    }

    override fun setSegments(
        segments: MutableList<Long>?,
        segmentLen: Int,
        segmentStartIndex: Int
    ) {
        mFilmVideo?.setSegments(segments, segmentLen, segmentStartIndex)
    }


    override fun changeResolution(resolution: Resolution, url: String) {
        mFilmVideo?.changeResolution(
            resolution,
            url,
            mFilmPlayerFactory,
            ShPlayerConfig.enableSeamlessChangeResolution
        )
    }

    override fun getFilmCurrentPosition(): Long {
        return mFilmVideo?.currentPosition ?: 0
    }

    override fun getFilmDuration(): Long {
        return mFilmVideo?.duration ?: 0
    }

    override fun getFilmVideoController(): VideoController? {
        return mFilmVideo?.videoController
    }

    override fun <T : BaseOptionModel> setOption(option: T?) {
        mFilmVideo?.setOption(option)
    }

    override fun changeFilmRenderFactory(renderFactory: RenderFactory) {
        mFilmVideo?.renderFactory = renderFactory
    }

    override fun changeAdRenderFactory(renderFactory: RenderFactory) {
        mAdVideo?.renderFactory = renderFactory

    }

    inner class AdStateChangeListener : OnStateChangeListener {
        override fun onScreenModeChanged(screenMode: Int) {
            videoViewController?.notifyVideoScreenModeChanged(screenMode)
        }

        override fun onPlayerStateChanged(playState: Int, extras: HashMap<String, Any>) {
            videoViewController?.notifyVideoAdPlayStateChanged(playState, extras)
        }
    }

    private var adTsHasCompleted=false
    private var adTsTimer: Interval? = null

    inner class AdTsStateChangeListener : OnStateChangeListener {
        override fun onScreenModeChanged(screenMode: Int) {
            videoViewController?.notifyVideoScreenModeChanged(screenMode)
        }

        override fun onPlayerStateChanged(playState: Int, extras: HashMap<String, Any>) {
            when (playState) {
                PlayerConstants.VideoState.PREPARING->{
                    addAdTs()
                }
                PlayerConstants.VideoState.PREPARED->{
                    mFilmVideo?.pause()
                }
                PlayerConstants.VideoState.PLAYING,
                PlayerConstants.VideoState.PLAYING_BACK->{
                    mFilmVideo?.let {
                        removeView(it)
                    }
                    // 如果是从暂停状态恢复，继续倒计时；否则开启新的倒计时
                    if (adTsTimer?.state == IntervalStatus.STATE_PAUSE) {
                        adTsTimer?.resume()
                        ShVideoLogger.d("AdTs countdown resumed")
                    } else {
                        // 开启14.5秒倒计时，倒计时结束后移除广告并加回正片
                        startAdTsCountdown()
                    }
                }
                PlayerConstants.VideoState.PAUSED -> {
                    // 暂停时暂停倒计时
                    adTsTimer?.pause()
                    ShVideoLogger.d("AdTs countdown paused")
                }
                PlayerConstants.VideoState.ERROR -> {
                    // 出错时立即移除广告并加回正片
                    cancelAdTsCountdown()
                    completeAdTsAndStartFilm()
                }
            }
//            videoViewController?.notifyVideoFilmPlayStateChanged(playState, extras)
        }
    }

    inner class FilmStateChangeListener : OnStateChangeListener {
        override fun onScreenModeChanged(screenMode: Int) {
            videoViewController?.notifyVideoScreenModeChanged(screenMode)
        }

        override fun onPlayerStateChanged(playState: Int, extras: HashMap<String, Any>) {
            videoViewController?.notifyVideoFilmPlayStateChanged(playState, extras)
            if (playState == PlayerConstants.VideoState.PREPARED) {
                ShVideoLogger.d("requestLogoAd  startAdLogo")
                mFilmVideo?.startAdLogo()
            }
            if (playState == PlayerConstants.VideoState.PAUSED) {
                ShVideoLogger.d("requestLogoAd  pause")
                mShowTimer?.pause()
            }
            if (playState == PlayerConstants.VideoState.PLAYING || playState == PlayerConstants.VideoState.PLAYING_BACK) {
                VideoStartTimeUtil.getInstants().playingFilmTime()
                mShowTimer?.switch()
                ShVideoLogger.d("requestLogoAd  switch：${mShowTimer?.state?.name}")
            }
        }
    }


    override fun onLogoAd() {
        ShVideoLogger.d("requestLogoAd  ")
        onCreateTimer()
    }

    private var mShowTimer: Interval? = null

    private fun resetShow() {
        mShowTimer?.cancel()
        mShowTimer = null
        ShVideoLogger.d("requestLogoAd resetShow ")
    }

    private fun onCreateTimer() {
        resetShow()
        mShowTimer = Interval(60L, 1, TimeUnit.SECONDS, 0).subscribe {
            ShVideoLogger.d("requestLogoAd onCreateShowTimer:$it")
        }.finish {
            resetShow()
            requestLogoAd()
        }
    }

    /**
     * 开启广告倒计时，14.5秒后移除广告并加回正片
     */
    private fun startAdTsCountdown() {
        // 如果已经完成了，不再开启倒计时
        if (adTsHasCompleted) {
            return
        }

        // 取消之前的倒计时
        cancelAdTsCountdown()

        // 创建14.5秒的倒计时（14500毫秒）
        adTsTimer = Interval(14500L, 100, TimeUnit.MILLISECONDS, 0).subscribe { currentTime ->
            ShVideoLogger.d("AdTs countdown: ${currentTime}ms")
        }.finish {
            ShVideoLogger.d("AdTs countdown finished, removing ad and starting film")
            completeAdTsAndStartFilm()
        }.start()
    }

    /**
     * 取消广告倒计时
     */
    private fun cancelAdTsCountdown() {
        adTsTimer?.cancel()
        adTsTimer = null
    }

    /**
     * 完成广告播放并开始正片
     */
    private fun completeAdTsAndStartFilm() {
        if (adTsHasCompleted) {
            return
        }

        adTsHasCompleted = true
        cancelAdTsCountdown()

        mFilmVideo?.let {
            addView(it)
        }
        mFilmVideo?.start()
        removeAdTs()

        ShVideoLogger.d("AdTs completed, film started")
    }
}